#ifndef APPCONFIG_H
#define APPCONFIG_H

#include <QString>
#include <QSerialPort>

/**
 * @brief 串口配置结构体
 */
struct SerialConfig {
    QString portName;                           // 串口名称
    QSerialPort::BaudRate baudRate;            // 波特率
    QSerialPort::DataBits dataBits;            // 数据位
    QSerialPort::Parity parity;                // 校验位
    QSerialPort::StopBits stopBits;            // 停止位
    QSerialPort::FlowControl flowControl;      // 流控制
    int readTimeout;                           // 读超时(ms)
    int writeTimeout;                          // 写超时(ms)

    SerialConfig()
        : portName("ttySTM3")
        , baudRate(QSerialPort::Baud115200)
        , dataBits(QSerialPort::Data8)
        , parity(QSerialPort::NoParity)
        , stopBits(QSerialPort::OneStop)
        , flowControl(QSerialPort::NoFlowControl)
        , readTimeout(3000)
        , writeTimeout(3000)
    {}
};

/**
 * @brief 日志配置结构体
 */
struct LogConfig {
    QString logLevel;                          // 日志级别
    QString logFilePath;                       // 日志文件路径
    int maxFileSize;                           // 最大文件大小(MB)
    int maxFileCount;                          // 最大文件数量
    bool enableConsoleOutput;                  // 是否启用控制台输出

    LogConfig()
        : logLevel("INFO")
        , logFilePath("logs/app.log")
        , maxFileSize(10)
        , maxFileCount(5)
        , enableConsoleOutput(true)
    {}
};

/**
 * @brief MQTT配置结构体 (预留扩展)
 */
struct MqttConfig {
    QString host;                              // MQTT服务器地址
    quint16 port;                              // MQTT服务器端口
    QString clientId;                          // 客户端ID
    QString username;                          // 用户名
    QString password;                          // 密码
    int keepAlive;                             // 心跳间隔(秒)
    bool cleanSession;                         // 清理会话

    MqttConfig()
        : host("localhost")
        , port(1883)
        , clientId("SerialMqttBridge")
        , username("")
        , password("")
        , keepAlive(60)
        , cleanSession(true)
    {}
};

/**
 * @brief 应用程序配置结构体
 */
struct AppConfig {
    SerialConfig serial;                       // 串口配置
    LogConfig log;                             // 日志配置
    MqttConfig mqtt;                           // MQTT配置(预留)

    AppConfig() = default;
};

#endif // APPCONFIG_H
